<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00BFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E90FF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="200" height="200" fill="#1a1a4a"/>
  
  <!-- Infinity symbol -->
  <path d="M50 100 C50 80, 70 60, 100 80 C130 60, 150 80, 150 100 C150 120, 130 140, 100 120 C70 140, 50 120, 50 100 Z" 
        fill="url(#orangeGradient)" stroke="url(#orangeGradient)" stroke-width="3"/>
  
  <path d="M150 100 C150 80, 130 60, 100 80 C70 60, 50 80, 50 100 C50 120, 70 140, 100 120 C130 140, 150 120, 150 100 Z" 
        fill="url(#blueGradient)" stroke="url(#blueGradient)" stroke-width="3" opacity="0.8"/>
  
  <!-- Arabic text "الرايه" -->
  <text x="100" y="160" font-family="Arial, sans-serif" font-size="24" font-weight="bold" 
        text-anchor="middle" fill="#FFA500" direction="rtl">الرايه</text>
</svg>
