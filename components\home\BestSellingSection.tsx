"use client"

import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"

export function BestSellingSection() {
  const router = useRouter()

  const popularGames = [
    {
      id: "pubg-mobile",
      name: "PUBG Mobile",
      image: "🎯",
      gradient: "from-orange-500 to-red-500",
      isHot: true
    },
    {
      id: "free-fire",
      name: "Free Fire",
      image: "🔥",
      gradient: "from-blue-500 to-purple-500",
      isHot: false
    },
    {
      id: "clash-of-clans",
      name: "Clash of Clans",
      image: "⚔️",
      gradient: "from-green-500 to-blue-500",
      isHot: false
    },
    {
      id: "call-of-duty",
      name: "Call of Duty",
      image: "🎮",
      gradient: "from-gray-500 to-slate-600",
      isHot: false
    }
  ]

  const handleBestSellingClick = () => {
    router.push("/store")
  }

  const handleGameClick = (gameId: string) => {
    router.push(`/store/${gameId}`)
  }

  return (
    <section className="space-y-6">
      {/* Section Title */}
      <div className="text-center space-y-4">
        <h3 className="text-white font-bold text-xl lg:text-2xl">
          الرايه لشحن الألعاب الإلكترونية , أفضل
        </h3>
        
        {/* Golden CTA Button */}
        <Button
          onClick={handleBestSellingClick}
          className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold text-lg lg:text-xl px-8 lg:px-12 py-4 lg:py-6 rounded-full shadow-2xl hover:scale-105 transition-all duration-300 border-2 border-yellow-300/50"
        >
          الأكثر مبيعاً
        </Button>
      </div>

      {/* Popular Games Grid */}
      <div className="grid grid-cols-2 gap-4 lg:gap-6 max-w-md mx-auto">
        {popularGames.map((game) => (
          <div
            key={game.id}
            onClick={() => handleGameClick(game.id)}
            className={`relative rounded-2xl overflow-hidden h-32 lg:h-40 bg-gradient-to-br ${game.gradient} shadow-xl hover:scale-105 transition-all duration-300 cursor-pointer group`}
          >
            {/* HOT Badge */}
            {game.isHot && (
              <div className="absolute top-2 left-2 z-20">
                <div className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg transform -rotate-12">
                  HOT
                </div>
              </div>
            )}

            {/* Game Content */}
            <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-300" />
            <div className="relative h-full p-4 flex flex-col justify-center items-center text-center">
              <div className="text-4xl lg:text-5xl mb-2 group-hover:scale-110 transition-transform duration-300">
                {game.image}
              </div>
              <h4 className="text-white font-bold text-sm lg:text-base drop-shadow-lg">
                {game.name}
              </h4>
            </div>

            {/* Shine effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
          </div>
        ))}
      </div>
    </section>
  )
}
