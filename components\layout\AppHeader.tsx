"use client"

import { Grid3X3, ShoppingBag, Wallet } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"

interface AppHeaderProps {
  onMenuOpen: () => void
}

export function AppHeader({ onMenuOpen }: AppHeaderProps) {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-slate-800/95 backdrop-blur-xl border-b border-slate-700/50 shadow-2xl">
      {/* Main Header Row */}
      <div className="flex items-center justify-between p-4 lg:p-6">
        {/* Left Side - Profile Avatar */}
        <div className="flex items-center">
          <div className="w-12 h-12 lg:w-14 lg:h-14 rounded-full overflow-hidden border-2 border-yellow-400/50 shadow-lg">
            <Image
              src="/logo-without-background.png"
              alt="Profile Avatar"
              width={56}
              height={56}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        {/* Center - Logo */}
        <div className="flex items-center justify-center">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <Image
              src="/logo-without-background.png"
              alt="الرايه"
              width={40}
              height={40}
              className="w-8 h-8 lg:w-10 lg:h-10"
            />
            <span className="text-white font-bold text-lg lg:text-xl">الرايه</span>
          </div>
        </div>

        {/* Right Side - Menu and Cart */}
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Button
            variant="ghost"
            size="icon"
            className="text-white bg-gradient-to-r from-pink-500 to-rose-500 rounded-full hover:scale-110 transition-all duration-300 shadow-lg"
          >
            <ShoppingBag className="h-5 w-5 lg:h-6 lg:w-6" />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            className="text-white hover:bg-white/10 transition-all duration-300 hover:scale-110"
            onClick={onMenuOpen}
          >
            <Grid3X3 className="h-6 w-6" />
          </Button>
        </div>
      </div>

      {/* Wallet Balance Row */}
      <div className="px-4 lg:px-6 pb-3">
        <div className="flex items-center justify-start">
          <div className="flex items-center space-x-2 rtl:space-x-reverse bg-slate-700/50 rounded-lg px-3 py-2 backdrop-blur-sm">
            <Wallet className="h-4 w-4 text-yellow-400" />
            <span className="text-yellow-400 font-bold text-sm lg:text-base">10,000</span>
            <span className="text-slate-300 text-xs lg:text-sm">ر.س</span>
          </div>
        </div>
      </div>
    </header>
  )
}
