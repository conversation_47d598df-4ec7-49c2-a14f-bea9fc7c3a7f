"use client"

import { Grid3X3, Wallet } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"

interface AppHeaderProps {
  onMenuOpen: () => void
}

export function AppHeader({ onMenuOpen }: AppHeaderProps) {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-slate-800/95 backdrop-blur-xl border-b border-slate-700/50 shadow-2xl">
      {/* Single Header Row */}
      <div className="flex items-center justify-between p-4 lg:p-6">
        {/* Left Side - Menu Icon */}
        <Button
          variant="ghost"
          size="icon"
          className="text-white hover:bg-white/10 transition-all duration-300 hover:scale-110"
          onClick={onMenuOpen}
        >
          <Grid3X3 className="h-6 w-6" />
        </Button>

        {/* Center - Larger Logo */}
        <div className="flex items-center justify-center flex-1">
          <Image
            src="/logo-without-background.png"
            alt="الرايه"
            width={80}
            height={80}
            className="w-16 h-16 lg:w-20 lg:h-20"
          />
        </div>

        {/* Right Side - Wallet Balance */}
        <div className="flex items-center space-x-1 rtl:space-x-reverse bg-slate-700/50 rounded-lg px-2 py-1 backdrop-blur-sm">
          <Wallet className="h-3 w-3 text-yellow-400" />
          <span className="text-yellow-400 font-bold text-xs lg:text-sm">10,000</span>
          <span className="text-slate-300 text-xs">ر.س</span>
        </div>
      </div>
    </header>
  )
}
