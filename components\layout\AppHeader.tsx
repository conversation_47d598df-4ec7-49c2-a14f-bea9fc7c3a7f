"use client"

import { Menu } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"

interface AppHeaderProps {
  onMenuOpen: () => void
}

export function AppHeader({ onMenuOpen }: AppHeaderProps) {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-slate-800/95 backdrop-blur-xl border-b border-slate-700/50 shadow-2xl">
      {/* Header Row with 3 equal sections */}
      <div className="flex items-center justify-between p-3 lg:p-4">
        {/* Left Side - Profile Picture */}
        <div className="flex items-center justify-start w-16 lg:w-20">
          <div className="w-12 h-12 lg:w-14 lg:h-14 rounded-full overflow-hidden border-2 border-white/20 shadow-lg">
            <Image
              src="/logo-without-background.png"
              alt="Profile"
              width={56}
              height={56}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        {/* Center - Logo */}
        <div className="flex items-center justify-center flex-1">
          <Image
            src="/logo-without-background.png"
            alt="الرايه"
            width={120}
            height={120}
            className="w-16 h-16 lg:w-20 lg:h-20"
          />
        </div>

        {/* Right Side - Menu Widget */}
        <div className="flex items-center justify-end w-16 lg:w-20">
          <Button
            variant="ghost"
            size="icon"
            className="text-white hover:bg-white/10 transition-all duration-300 hover:scale-110 p-2"
            onClick={onMenuOpen}
          >
            <Menu className="h-6 w-6 lg:h-7 lg:w-7" />
          </Button>
        </div>
      </div>
    </header>
  )
}
