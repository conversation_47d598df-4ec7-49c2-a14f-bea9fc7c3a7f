"use client"

import { Grid3X3, Wallet } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"

interface AppHeaderProps {
  onMenuOpen: () => void
}

export function AppHeader({ onMenuOpen }: AppHeaderProps) {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-slate-800/95 backdrop-blur-xl border-b border-slate-700/50 shadow-2xl">
      {/* Single Header Row */}
      <div className="flex items-center justify-between p-4 lg:p-6">
        {/* Left Side - Menu Icon */}
        <Button
          variant="ghost"
          size="icon"
          className="text-white hover:bg-white/10 transition-all duration-300 hover:scale-110"
          onClick={onMenuOpen}
        >
          <Grid3X3 className="h-6 w-6" />
        </Button>

        {/* Center - Larger Logo */}
        <div className="flex items-center justify-center">
          <Image
            src="/logo-without-background.png"
            alt="الرايه"
            width={64}
            height={64}
            className="w-12 h-12 lg:w-16 lg:h-16"
          />
        </div>

        {/* Right Side - Wallet Balance */}
        <div className="flex items-center space-x-2 rtl:space-x-reverse bg-slate-700/50 rounded-lg px-3 py-2 backdrop-blur-sm">
          <Wallet className="h-4 w-4 text-yellow-400" />
          <span className="text-yellow-400 font-bold text-sm lg:text-base">10,000</span>
          <span className="text-slate-300 text-xs lg:text-sm">ر.س</span>
        </div>
      </div>
    </header>
  )
}
