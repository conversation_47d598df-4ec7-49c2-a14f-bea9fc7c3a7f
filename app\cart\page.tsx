"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, Minus, Plus, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import { useRouter } from "next/navigation"

interface CartItem {
  id: string
  name: string
  price: number
  quantity: number
  image: string
}

export default function CartPage() {
  const router = useRouter()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [cartItems, setCartItems] = useState<CartItem[]>([
    {
      id: "1",
      name: "FREE FIRE - 100 جوهرة",
      price: 15,
      quantity: 2,
      image: "🔥"
    },
    {
      id: "2", 
      name: "PUBG Mobile - 60 UC",
      price: 25,
      quantity: 1,
      image: "🎯"
    }
  ])

  const updateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity === 0) {
      setCartItems(cartItems.filter(item => item.id !== id))
    } else {
      setCartItems(cartItems.map(item => 
        item.id === id ? { ...item, quantity: newQuantity } : item
      ))
    }
  }

  const removeItem = (id: string) => {
    setCartItems(cartItems.filter(item => item.id !== id))
  }

  const totalPrice = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0.6))]" />
      <div className="absolute top-0 left-1/4 w-72 h-72 bg-blue-500/20 rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl animate-pulse delay-1000" />

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 p-4 lg:p-8 space-y-6 pb-32 pt-32 lg:pt-36 max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center space-x-4 rtl:space-x-reverse mb-6">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl lg:text-3xl font-bold text-white">سلة التسوق</h1>
        </div>

        {cartItems.length === 0 ? (
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 p-8 text-center">
            <div className="text-6xl mb-4">🛒</div>
            <h2 className="text-xl font-bold text-white mb-2">سلة التسوق فارغة</h2>
            <p className="text-slate-400 mb-4">لم تقم بإضافة أي منتجات بعد</p>
            <Button 
              onClick={() => router.push('/store')}
              className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
            >
              تصفح المتجر
            </Button>
          </Card>
        ) : (
          <>
            {/* Cart Items */}
            <div className="space-y-4">
              {cartItems.map((item) => (
                <Card key={item.id} className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 rtl:space-x-reverse">
                      <div className="text-3xl">{item.image}</div>
                      <div>
                        <h3 className="font-bold text-white">{item.name}</h3>
                        <p className="text-yellow-400 font-bold">{item.price} ر.س</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeItem(item.id)}
                        className="text-red-400 hover:bg-red-500/20"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                      
                      <div className="flex items-center space-x-2 rtl:space-x-reverse bg-slate-700/50 rounded-lg">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className="text-white hover:bg-white/10 h-8 w-8"
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <span className="text-white font-bold px-2">{item.quantity}</span>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className="text-white hover:bg-white/10 h-8 w-8"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Total & Checkout */}
            <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 p-6">
              <div className="flex items-center justify-between mb-4">
                <span className="text-xl font-bold text-white">المجموع الكلي:</span>
                <span className="text-2xl font-bold text-yellow-400">{totalPrice} ر.س</span>
              </div>
              <Button className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-bold py-3">
                إتمام الشراء
              </Button>
            </Card>
          </>
        )}
      </main>

      <MobileNavigation />
      <DesktopFooter />
    </div>
  )
}
