// Common types used across the application

export interface GameCard {
  title: string
  subtitle: string
  gradient: string
  icon: string
  hot?: boolean
}

export interface MenuItem {
  icon: React.ReactNode
  label: string
  href: string
}

export interface Slide {
  id: number
  title: string
  subtitle: string
  buttonText: string
  gradient: string
  image: string
}

export interface Feature {
  icon: string
  title: string
  desc: string
}

export interface Stat {
  number: string
  label: string
}

export interface NavigationItem {
  id: string
  icon: React.ReactNode
  label: string
  center?: boolean
}

// Wallet and Currency Types
export type Currency = "SDG" | "EGP"

export interface CurrencyInfo {
  code: Currency
  name: string
  symbol: string
  arabicName: string
}

export interface WalletBalance {
  currency: Currency
  amount: number
  lastUpdated: Date
}

export interface Transaction {
  id: string
  type: "deposit" | "withdrawal" | "purchase"
  amount: number
  currency: Currency
  description: string
  date: Date
  status: "completed" | "pending" | "failed"
  reference?: string
}

export interface WalletData {
  balances: WalletBalance[]
  selectedCurrency: Currency
  totalPurchases: number
  transactions: Transaction[]
}
