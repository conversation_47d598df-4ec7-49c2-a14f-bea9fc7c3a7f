"use client"

import { Wallet, ShoppingCart } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"

export function WalletCartSection() {
  const router = useRouter()

  const handleCartClick = () => {
    router.push('/cart')
  }

  const handleWalletClick = () => {
    router.push('/wallet')
  }

  return (
    <div className="fixed top-16 lg:top-20 left-0 right-0 z-40 bg-slate-800/95 backdrop-blur-xl border-b border-slate-700/50">
      <div className="flex items-center justify-between px-3 lg:px-4 py-2">
        {/* Left Side - Wallet Balance */}
        <Button
          variant="ghost"
          onClick={handleWalletClick}
          className="flex items-center space-x-2 rtl:space-x-reverse bg-slate-700/50 rounded-lg px-3 py-1 backdrop-blur-sm hover:bg-slate-600/50 transition-all duration-300"
        >
          <Wallet className="h-4 w-4 text-yellow-400" />
          <span className="text-yellow-400 font-bold text-sm">10,000</span>
          <span className="text-slate-300 text-xs">ر.س</span>
        </Button>

        {/* Right Side - Shopping Cart */}
        <Button
          variant="ghost"
          size="icon"
          className="text-white hover:bg-white/10 transition-all duration-300 hover:scale-110 bg-slate-700/50 rounded-lg p-2"
          onClick={handleCartClick}
        >
          <ShoppingCart className="h-5 w-5" />
        </Button>
      </div>
    </div>
  )
}
