"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { TransactionItem } from "@/components/wallet/TransactionItem"
import { Transaction, Currency } from "@/lib/types"
import { filterTransactionsByType } from "@/lib/data/mockWalletData"
import { History, TrendingDown, TrendingUp, ShoppingCart } from "lucide-react"

interface WalletTransactionsProps {
  transactions: Transaction[]
  selectedCurrency: Currency
  isLoading: boolean
}

export function WalletTransactions({
  transactions,
  selectedCurrency,
  isLoading
}: WalletTransactionsProps) {
  const [activeTab, setActiveTab] = useState("all")

  // ## Filter transactions by type and currency - will be replaced with Supabase queries
  const getFilteredTransactions = (type?: "deposit" | "withdrawal" | "purchase") => {
    let filtered = filterTransactionsByType(transactions, type)
    // Filter by selected currency
    filtered = filtered.filter(t => t.currency === selectedCurrency)
    // Sort by date (newest first)
    return filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  }

  const allTransactions = getFilteredTransactions()
  const deposits = getFilteredTransactions("deposit")
  const withdrawals = getFilteredTransactions("withdrawal")
  const purchases = getFilteredTransactions("purchase")

  const EmptyState = ({ type }: { type: string }) => (
    <div className="text-center py-12 space-y-4">
      <div className="w-16 h-16 mx-auto bg-slate-700/50 rounded-full flex items-center justify-center">
        <History className="h-8 w-8 text-slate-400" />
      </div>
      <div className="space-y-2">
        <h3 className="text-lg font-medium text-slate-300">لا توجد معاملات متاحة</h3>
        <p className="text-sm text-slate-400">
          {type === "all" && "لم تقم بأي معاملات بعد"}
          {type === "deposits" && "لم تقم بأي إيداعات بعد"}
          {type === "withdrawals" && "لم تقم بأي سحوبات بعد"}
          {type === "purchases" && "لم تقم بأي مشتريات بعد"}
        </p>
      </div>
    </div>
  )

  const TransactionsList = ({ transactionsList, type }: { transactionsList: Transaction[], type: string }) => {
    if (isLoading) {
      return (
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center gap-4 p-4 bg-slate-700/30 rounded-xl">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
              <Skeleton className="h-6 w-20" />
            </div>
          ))}
        </div>
      )
    }

    if (transactionsList.length === 0) {
      return <EmptyState type={type} />
    }

    return (
      <div className="space-y-3">
        {/* ## Transaction list - data from Supabase transactions table */}
        {transactionsList.map((transaction) => (
          <TransactionItem
            key={transaction.id}
            transaction={transaction}
          />
        ))}
      </div>
    )
  }

  return (
    <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3 text-xl text-white">
          <div className="p-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-xl">
            <History className="h-6 w-6 text-white" />
          </div>
          المعاملات
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* ## Transactions tabs - filtering will be done via Supabase queries */}
        {/* Modern Mobile-First Filter Design */}
        <div className="space-y-4">
          {/* Filter Chips - Mobile Optimized */}
          <div className="flex flex-wrap gap-2 lg:gap-3">
            <button
              onClick={() => setActiveTab("all")}
              className={cn(
                "flex items-center gap-2 px-4 py-3 rounded-full text-sm font-medium transition-all duration-300 border-2",
                activeTab === "all"
                  ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 border-yellow-400 shadow-lg scale-105"
                  : "bg-slate-700/50 text-slate-300 border-slate-600/50 hover:bg-slate-600/50 hover:border-slate-500"
              )}
            >
              <History className="h-4 w-4" />
              <span>جميع المعاملات</span>
            </button>

            <button
              onClick={() => setActiveTab("deposits")}
              className={cn(
                "flex items-center gap-2 px-4 py-3 rounded-full text-sm font-medium transition-all duration-300 border-2",
                activeTab === "deposits"
                  ? "bg-gradient-to-r from-green-400 to-green-500 text-white border-green-400 shadow-lg scale-105"
                  : "bg-slate-700/50 text-slate-300 border-slate-600/50 hover:bg-slate-600/50 hover:border-slate-500"
              )}
            >
              <TrendingUp className="h-4 w-4" />
              <span>الإيداعات</span>
            </button>

            <button
              onClick={() => setActiveTab("withdrawals")}
              className={cn(
                "flex items-center gap-2 px-4 py-3 rounded-full text-sm font-medium transition-all duration-300 border-2",
                activeTab === "withdrawals"
                  ? "bg-gradient-to-r from-red-400 to-red-500 text-white border-red-400 shadow-lg scale-105"
                  : "bg-slate-700/50 text-slate-300 border-slate-600/50 hover:bg-slate-600/50 hover:border-slate-500"
              )}
            >
              <TrendingDown className="h-4 w-4" />
              <span>السحوبات</span>
            </button>

            <button
              onClick={() => setActiveTab("purchases")}
              className={cn(
                "flex items-center gap-2 px-4 py-3 rounded-full text-sm font-medium transition-all duration-300 border-2",
                activeTab === "purchases"
                  ? "bg-gradient-to-r from-blue-400 to-blue-500 text-white border-blue-400 shadow-lg scale-105"
                  : "bg-slate-700/50 text-slate-300 border-slate-600/50 hover:bg-slate-600/50 hover:border-slate-500"
              )}
            >
              <ShoppingCart className="h-4 w-4" />
              <span>المشتريات</span>
            </button>
          </div>
        </div>

        {/* Content Area */}
        <div className="space-y-4">
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center gap-4 p-4 bg-slate-700/30 rounded-xl">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                  <Skeleton className="h-6 w-20" />
                </div>
              ))}
            </div>
          ) : filteredTransactions.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-slate-400 text-lg mb-2">لا توجد معاملات</div>
              <div className="text-slate-500 text-sm">
                {activeTab === "all" && "لم تقم بأي معاملات بعد"}
                {activeTab === "deposits" && "لم تقم بأي إيداعات بعد"}
                {activeTab === "withdrawals" && "لم تقم بأي سحوبات بعد"}
                {activeTab === "purchases" && "لم تقم بأي مشتريات بعد"}
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredTransactions.map((transaction) => (
                <TransactionItem
                  key={transaction.id}
                  transaction={transaction}
                />
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
