"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { TransactionItem } from "@/components/wallet/TransactionItem"
import { Transaction, Currency } from "@/lib/types"
import { filterTransactionsByType } from "@/lib/data/mockWalletData"
import { History, TrendingDown, TrendingUp, ShoppingCart } from "lucide-react"

interface WalletTransactionsProps {
  transactions: Transaction[]
  selectedCurrency: Currency
  isLoading: boolean
}

export function WalletTransactions({
  transactions,
  selectedCurrency,
  isLoading
}: WalletTransactionsProps) {
  const [activeTab, setActiveTab] = useState("all")

  // ## Filter transactions by type and currency - will be replaced with Supabase queries
  const getFilteredTransactions = (type?: "deposit" | "withdrawal" | "purchase") => {
    let filtered = filterTransactionsByType(transactions, type)
    // Filter by selected currency
    filtered = filtered.filter(t => t.currency === selectedCurrency)
    // Sort by date (newest first)
    return filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  }

  const allTransactions = getFilteredTransactions()
  const deposits = getFilteredTransactions("deposit")
  const withdrawals = getFilteredTransactions("withdrawal")
  const purchases = getFilteredTransactions("purchase")

  const EmptyState = ({ type }: { type: string }) => (
    <div className="text-center py-12 space-y-4">
      <div className="w-16 h-16 mx-auto bg-slate-700/50 rounded-full flex items-center justify-center">
        <History className="h-8 w-8 text-slate-400" />
      </div>
      <div className="space-y-2">
        <h3 className="text-lg font-medium text-slate-300">لا توجد معاملات متاحة</h3>
        <p className="text-sm text-slate-400">
          {type === "all" && "لم تقم بأي معاملات بعد"}
          {type === "deposits" && "لم تقم بأي إيداعات بعد"}
          {type === "withdrawals" && "لم تقم بأي سحوبات بعد"}
          {type === "purchases" && "لم تقم بأي مشتريات بعد"}
        </p>
      </div>
    </div>
  )

  const TransactionsList = ({ transactionsList, type }: { transactionsList: Transaction[], type: string }) => {
    if (isLoading) {
      return (
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center gap-4 p-4 bg-slate-700/30 rounded-xl">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
              <Skeleton className="h-6 w-20" />
            </div>
          ))}
        </div>
      )
    }

    if (transactionsList.length === 0) {
      return <EmptyState type={type} />
    }

    return (
      <div className="space-y-3">
        {/* ## Transaction list - data from Supabase transactions table */}
        {transactionsList.map((transaction) => (
          <TransactionItem
            key={transaction.id}
            transaction={transaction}
          />
        ))}
      </div>
    )
  }

  return (
    <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3 text-xl text-white">
          <div className="p-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-xl">
            <History className="h-6 w-6 text-white" />
          </div>
          المعاملات
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* ## Transactions tabs - filtering will be done via Supabase queries */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-slate-700/50 rounded-xl p-1">
            <TabsTrigger 
              value="all" 
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-yellow-400 data-[state=active]:to-orange-500 data-[state=active]:text-slate-900 rounded-lg"
            >
              <History className="h-4 w-4 ml-1" />
              الكل
            </TabsTrigger>
            <TabsTrigger 
              value="deposits"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-400 data-[state=active]:to-green-500 data-[state=active]:text-white rounded-lg"
            >
              <TrendingUp className="h-4 w-4 ml-1" />
              الإيداعات
            </TabsTrigger>
            <TabsTrigger 
              value="withdrawals"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-red-400 data-[state=active]:to-red-500 data-[state=active]:text-white rounded-lg"
            >
              <TrendingDown className="h-4 w-4 ml-1" />
              السحوبات
            </TabsTrigger>
            <TabsTrigger 
              value="purchases"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-400 data-[state=active]:to-blue-500 data-[state=active]:text-white rounded-lg"
            >
              <ShoppingCart className="h-4 w-4 ml-1" />
              المشتريات
            </TabsTrigger>
          </TabsList>

          <div className="mt-6">
            <TabsContent value="all" className="mt-0">
              <TransactionsList transactionsList={allTransactions} type="all" />
            </TabsContent>

            <TabsContent value="deposits" className="mt-0">
              <TransactionsList transactionsList={deposits} type="deposits" />
            </TabsContent>

            <TabsContent value="withdrawals" className="mt-0">
              <TransactionsList transactionsList={withdrawals} type="withdrawals" />
            </TabsContent>

            <TabsContent value="purchases" className="mt-0">
              <TransactionsList transactionsList={purchases} type="purchases" />
            </TabsContent>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  )
}
