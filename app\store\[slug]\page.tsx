import { notFound } from "next/navigation"
import { ProductStandard } from "@/components/products/ProductStandard"
import { ProductInstant } from "@/components/products/ProductInstant"

// ## Mock product data - will be replaced with Supabase queries
const mockProducts = {
  "pubg-mobile-uc": {
    id: "pubg-mobile-uc",
    flow: "instant" as const,
    title: "شحن يوسي PUBG Mobile",
    shortDescription: "شحن فوري لعملة UC في لعبة PUBG Mobile - احصل على يوسي فوراً",
    image: "/images/products/pubg-mobile.jpg",
    category: "ألعاب الموبايل",
    rating: 4.8,
    reviewsCount: 1250,
    estimatedTime: "أقل من دقيقة",
    packs: [
      {
        id: "uc-60",
        name: "60 UC",
        amount: "60 يوسي",
        price: 25,
        originalPrice: 30,
        discount: 17
      },
      {
        id: "uc-325",
        name: "325 UC",
        amount: "325 يوسي",
        price: 120,
        originalPrice: 150,
        discount: 20,
        popular: true
      },
      {
        id: "uc-660",
        name: "660 UC",
        amount: "660 يوسي",
        price: 240,
        originalPrice: 300,
        discount: 20
      },
      {
        id: "uc-1800",
        name: "1800 UC",
        amount: "1800 يوسي",
        price: 600,
        originalPrice: 750,
        discount: 20
      }
    ],
    servers: [
      { id: "global", name: "Global", flag: "🌍" },
      { id: "kr", name: "Korea", flag: "🇰🇷" },
      { id: "jp", name: "Japan", flag: "🇯🇵" },
      { id: "sea", name: "Southeast Asia", flag: "🌏" }
    ],
    instructions: [
      "أدخل معرف اللاعب (Player ID) الخاص بك",
      "اختر السيرفر المناسب",
      "حدد كمية UC المطلوبة",
      "أكمل عملية الدفع",
      "ستصل UC إلى حسابك خلال دقائق"
    ],
    features: [
      "شحن فوري خلال دقائق",
      "دعم جميع السيرفرات",
      "أسعار تنافسية",
      "دعم فني 24/7",
      "ضمان الاسترداد"
    ]
  },
  "free-fire-diamonds": {
    id: "free-fire-diamonds",
    flow: "instant" as const,
    title: "شحن جواهر Free Fire",
    shortDescription: "شحن فوري للجواهر في لعبة Free Fire - احصل على الجواهر فوراً",
    image: "/images/products/free-fire.jpg",
    category: "ألعاب الموبايل",
    rating: 4.7,
    reviewsCount: 980,
    estimatedTime: "أقل من دقيقة",
    packs: [
      {
        id: "diamonds-100",
        name: "100 Diamonds",
        amount: "100 جوهرة",
        price: 35,
        originalPrice: 40,
        discount: 12
      },
      {
        id: "diamonds-310",
        name: "310 Diamonds",
        amount: "310 جوهرة",
        price: 100,
        originalPrice: 120,
        discount: 17,
        popular: true
      },
      {
        id: "diamonds-520",
        name: "520 Diamonds",
        amount: "520 جوهرة",
        price: 160,
        originalPrice: 200,
        discount: 20
      },
      {
        id: "diamonds-1080",
        name: "1080 Diamonds",
        amount: "1080 جوهرة",
        price: 320,
        originalPrice: 400,
        discount: 20
      }
    ],
    servers: [
      { id: "global", name: "Global", flag: "🌍" }
    ],
    instructions: [
      "أدخل معرف اللاعب (Player ID) الخاص بك",
      "حدد كمية الجواهر المطلوبة",
      "أكمل عملية الدفع",
      "ستصل الجواهر إلى حسابك خلال دقائق"
    ],
    features: [
      "شحن فوري خلال دقائق",
      "أسعار تنافسية",
      "دعم فني 24/7",
      "ضمان الاسترداد",
      "آمن ومضمون"
    ]
  }
}

interface PageProps {
  params: Promise<{
    slug: string
  }>
}

export default async function ProductPage({ params }: PageProps) {
  const { slug } = await params
  const product = mockProducts[slug as keyof typeof mockProducts]

  if (!product) {
    notFound()
  }

  // Render appropriate component based on product flow
  if (product.flow === "instant") {
    return (
      <ProductInstant 
        product={product}
      />
    )
  }

  return (
    <ProductStandard 
      product={product}
    />
  )
}

// ## Generate static params for known products - will be replaced with Supabase query
export async function generateStaticParams() {
  return Object.keys(mockProducts).map((slug) => ({
    slug,
  }))
}
