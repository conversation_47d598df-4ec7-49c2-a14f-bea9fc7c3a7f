"use client"

import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"

export function HeroPromoSection() {
  const gameIcons = [
    { name: "PUBG Mobile", icon: "🎯", color: "from-orange-500 to-red-500" },
    { name: "Free Fire", icon: "🔥", color: "from-blue-500 to-purple-500" },
    { name: "Clash of Clans", icon: "⚔️", color: "from-green-500 to-blue-500" },
    { name: "Call of Duty", icon: "🎮", color: "from-gray-500 to-slate-600" }
  ]

  return (
    <section className="relative rounded-3xl overflow-hidden bg-gradient-to-br from-slate-800/90 to-slate-900/90 backdrop-blur-xl border border-slate-700/50 shadow-2xl p-6 lg:p-8">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 right-0 w-32 h-32 bg-yellow-400 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-blue-400 rounded-full blur-2xl"></div>
      </div>

      <div className="relative z-10 flex flex-col lg:flex-row items-center gap-6 lg:gap-8">
        {/* Game Thumbnails Grid */}
        <div className="grid grid-cols-2 gap-3 lg:gap-4 flex-shrink-0">
          {gameIcons.map((game, index) => (
            <div
              key={index}
              className={`w-20 h-20 lg:w-24 lg:h-24 bg-gradient-to-br ${game.color} rounded-2xl flex items-center justify-center text-white text-2xl lg:text-3xl shadow-lg hover:scale-110 transition-all duration-300 cursor-pointer relative overflow-hidden group`}
            >
              <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-300"></div>
              <span className="relative z-10 drop-shadow-lg">{game.icon}</span>
              {/* Shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
            </div>
          ))}
        </div>

        {/* Promotional Content */}
        <div className="flex-1 text-center lg:text-right space-y-4">
          <h2 className="text-white font-bold text-xl lg:text-3xl leading-tight">
            يمكنك الآن شحن جميع العابك
          </h2>
          <p className="text-slate-300 text-sm lg:text-lg">
            شركة الرايه لشحن الألعاب الإلكترونية
          </p>
          <p className="text-slate-400 text-xs lg:text-base">
            و المزيد أيضاً
          </p>
        </div>

        {/* Diagonal Line Accent */}
        <div className="hidden lg:block absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-px h-24 bg-gradient-to-b from-transparent via-slate-600 to-transparent rotate-12"></div>
      </div>
    </section>
  )
}
