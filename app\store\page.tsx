"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import { 
  Search, 
  Filter, 
  Star, 
  Clock, 
  Zap, 
  ShoppingCart,
  Gamepad2,
  CreditCard,
  Gift
} from "lucide-react"

// ## Mock products data - will be replaced with Supabase queries
const mockProducts = [
  {
    id: "pubg-mobile-uc",
    title: "شحن يوسي PUBG Mobile",
    shortDescription: "شحن فوري لعملة UC في لعبة PUBG Mobile",
    image: "🎯",
    category: "ألعاب الموبايل",
    type: "instant",
    rating: 4.8,
    reviewsCount: 1250,
    priceFrom: 25,
    estimatedTime: "أقل من دقيقة",
    popular: true,
    features: ["شحن فوري", "دعم جميع السيرفرات", "أسعار تنافسية"]
  },
  {
    id: "free-fire-diamonds",
    title: "شحن جواهر Free Fire",
    shortDescription: "شحن فوري للجواهر في لعبة Free Fire",
    image: "🔥",
    category: "ألعاب الموبايل",
    type: "instant",
    rating: 4.7,
    reviewsCount: 980,
    priceFrom: 35,
    estimatedTime: "أقل من دقيقة",
    popular: true,
    features: ["شحن فوري", "دعم جميع السيرفرات", "أسعار تنافسية"]
  },
  {
    id: "clash-of-clans-gems",
    title: "شحن جواهر Clash of Clans",
    shortDescription: "شحن فوري للجواهر في لعبة Clash of Clans",
    image: "⚔️",
    category: "ألعاب الموبايل",
    type: "instant",
    rating: 4.6,
    reviewsCount: 750,
    priceFrom: 20,
    estimatedTime: "أقل من دقيقة",
    popular: false,
    features: ["شحن فوري", "دعم جميع السيرفرات", "أسعار تنافسية"]
  },
  {
    id: "call-of-duty-cp",
    title: "شحن نقاط Call of Duty",
    shortDescription: "شحن فوري لنقاط CP في لعبة Call of Duty Mobile",
    image: "🎮",
    category: "ألعاب الموبايل",
    type: "instant",
    rating: 4.5,
    reviewsCount: 650,
    priceFrom: 30,
    estimatedTime: "أقل من دقيقة",
    popular: false,
    features: ["شحن فوري", "دعم جميع السيرفرات", "أسعار تنافسية"]
  },
  {
    id: "steam-wallet",
    title: "بطاقات Steam Wallet",
    shortDescription: "بطاقات شحن محفظة Steam بفئات مختلفة",
    image: "💳",
    category: "بطاقات الألعاب",
    type: "standard",
    rating: 4.9,
    reviewsCount: 2100,
    priceFrom: 50,
    estimatedTime: "فوري",
    popular: true,
    features: ["تسليم فوري", "فئات متنوعة", "ضمان الجودة"]
  },
  {
    id: "playstation-plus",
    title: "اشتراك PlayStation Plus",
    shortDescription: "اشتراكات PlayStation Plus لجميع المناطق",
    image: "🎮",
    category: "اشتراكات",
    type: "standard",
    rating: 4.8,
    reviewsCount: 1800,
    priceFrom: 120,
    estimatedTime: "خلال ساعة",
    popular: true,
    features: ["جميع المناطق", "تفعيل فوري", "دعم فني"]
  }
]

const categories = ["الكل", "ألعاب الموبايل", "بطاقات الألعاب", "اشتراكات"]

export default function StorePage() {
  const [activeTab, setActiveTab] = useState("store")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("الكل")
  const [showFilters, setShowFilters] = useState(false)
  const router = useRouter()

  // Navigation handler
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "store") {
      router.push("/store")
    } else if (tab === "home") {
      router.push("/")
    } else {
      setActiveTab(tab)
    }
  }

  // Filter products based on search and category
  const filteredProducts = mockProducts.filter(product => {
    const matchesSearch = product.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.shortDescription.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "الكل" || product.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const handleProductClick = (productId: string) => {
    router.push(`/store/${productId}`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 p-4 lg:p-8 space-y-6 pb-32 pt-28 lg:pt-32 max-w-7xl mx-auto">
        {/* Page Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
            المتجر
          </h1>
          <p className="text-slate-300 text-lg">
            اكتشف أفضل العروض والمنتجات لألعابك المفضلة
          </p>
        </div>

        {/* Search and Filters */}
        <div className="space-y-4">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
            <Input
              placeholder="ابحث عن المنتجات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="bg-slate-800/50 border-slate-700 text-white placeholder-slate-400 pr-10 rounded-xl"
            />
          </div>

          {/* Category Filters */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className={`rounded-full transition-all duration-300 ${
                  selectedCategory === category
                    ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 hover:from-yellow-500 hover:to-orange-600"
                    : "border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white"
                }`}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>

        {/* Products Grid - 2 items per row */}
        <div className="grid grid-cols-2 gap-4 lg:gap-6">
          {filteredProducts.map((product) => (
            <Card
              key={product.id}
              onClick={() => handleProductClick(product.id)}
              className="bg-slate-800/50 border-slate-700 hover:bg-slate-700/50 transition-all duration-300 cursor-pointer group hover:scale-105 rounded-2xl overflow-hidden"
            >
              <CardContent className="p-4 space-y-3">
                {/* Product Image */}
                <div className="relative">
                  <div className="w-full h-24 lg:h-32 bg-gradient-to-br from-slate-700 to-slate-800 rounded-xl flex items-center justify-center text-4xl lg:text-5xl group-hover:scale-110 transition-transform duration-300">
                    {product.image}
                  </div>
                  {product.popular && (
                    <Badge className="absolute top-2 right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs">
                      شائع
                    </Badge>
                  )}
                </div>

                {/* Product Info */}
                <div className="space-y-2">
                  <h3 className="font-bold text-sm lg:text-base text-white group-hover:text-yellow-400 transition-colors duration-300 line-clamp-2">
                    {product.title}
                  </h3>
                  
                  <div className="flex items-center gap-2 text-xs text-slate-400">
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span>{product.rating}</span>
                    </div>
                    <span>({product.reviewsCount})</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-yellow-400 font-bold text-sm lg:text-base">
                      من {product.priceFrom} ر.س
                    </div>
                    <div className="flex items-center gap-1 text-xs text-slate-400">
                      {product.type === "instant" ? <Zap className="h-3 w-3" /> : <Clock className="h-3 w-3" />}
                      <span>{product.estimatedTime}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-bold text-slate-300 mb-2">لا توجد منتجات</h3>
            <p className="text-slate-400">جرب تغيير مصطلحات البحث أو الفلاتر</p>
          </div>
        )}
      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
