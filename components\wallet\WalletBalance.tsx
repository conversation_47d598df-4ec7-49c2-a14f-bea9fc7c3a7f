"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { CurrencySelector } from "@/components/wallet/CurrencySelector"
import { WalletData, Currency } from "@/lib/types"
import { getBalanceForCurrency } from "@/lib/data/mockWalletData"
import { formatCurrency } from "@/lib/data/currencies"
import { Wallet, Plus, TrendingUp } from "lucide-react"

interface WalletBalanceProps {
  walletData: WalletData
  selectedCurrency: Currency
  onCurrencyChange: (currency: Currency) => void
  onAddBalance: () => void
  isLoading: boolean
}

export function WalletBalance({
  walletData,
  selectedCurrency,
  onCurrencyChange,
  onAddBalance,
  isLoading
}: WalletBalanceProps) {
  // ## Get current balance for selected currency - will be replaced with Supabase query
  const currentBalance = getBalanceForCurrency(walletData, selectedCurrency)
  
  // ## Get total purchases in selected currency - will be calculated from Supabase
  const totalPurchases = walletData.totalPurchases

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Main Balance Card */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-xl text-white">
            <div className="p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl">
              <Wallet className="h-6 w-6 text-slate-900" />
            </div>
            رصيد يلا نلعب
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Currency Selector */}
          <div className="space-y-3">
            <p className="text-slate-300 text-sm font-medium">اختر العملة:</p>
            <CurrencySelector
              selectedCurrency={selectedCurrency}
              onCurrencyChange={onCurrencyChange}
              disabled={isLoading}
            />
          </div>

          {/* Current Balance Display */}
          <div className="space-y-2">
            <p className="text-slate-300 text-sm">رصيدك الحالي:</p>
            {isLoading ? (
              <Skeleton className="h-12 w-48" />
            ) : (
              <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                {/* ## Display formatted balance - data from Supabase wallet table */}
                {formatCurrency(currentBalance, selectedCurrency)}
              </div>
            )}
          </div>

          {/* Add Balance Button */}
          <Button
            onClick={onAddBalance}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold py-3 rounded-xl transition-all duration-300 hover:scale-105 shadow-lg"
          >
            <Plus className="h-5 w-5 ml-2" />
            إضافة رصيد
          </Button>
        </CardContent>
      </Card>

      {/* Statistics Card */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-xl text-white">
            <div className="p-2 bg-gradient-to-r from-green-400 to-blue-500 rounded-xl">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
            إحصائيات المحفظة
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Total Purchases */}
          <div className="space-y-2">
            <p className="text-slate-300 text-sm">إجمالي المشتريات:</p>
            {isLoading ? (
              <Skeleton className="h-8 w-32" />
            ) : (
              <div className="text-2xl font-bold text-green-400">
                {/* ## Display total purchases - calculated from Supabase transactions */}
                {formatCurrency(totalPurchases, selectedCurrency)}
              </div>
            )}
          </div>

          {/* Current Balance (Redundant Display) */}
          <div className="space-y-2">
            <p className="text-slate-300 text-sm">الرصيد الحالي:</p>
            {isLoading ? (
              <Skeleton className="h-8 w-32" />
            ) : (
              <div className="text-2xl font-bold text-blue-400">
                {/* ## Redundant balance display as requested */}
                {formatCurrency(currentBalance, selectedCurrency)}
              </div>
            )}
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-4 pt-4 border-t border-slate-700/50">
            <div className="text-center">
              <div className="text-lg font-bold text-yellow-400">
                {walletData.transactions.filter(t => t.type === 'deposit').length}
              </div>
              <div className="text-xs text-slate-400">إيداعات</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-orange-400">
                {walletData.transactions.filter(t => t.type === 'purchase').length}
              </div>
              <div className="text-xs text-slate-400">مشتريات</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
