"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { NewsTicket } from "@/components/shared/NewsTicket"
import { PromoSlider } from "@/components/home/<USER>"
import { BestSellingSection } from "@/components/home/<USER>"
import { GameCardsGrid } from "@/components/home/<USER>"
import { FeaturesSection } from "@/components/home/<USER>"
import { StatsSection } from "@/components/home/<USER>"
import { WalletCartSection } from "@/components/home/<USER>"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"

export function HomePage() {
  const [activeTab, setActiveTab] = useState("home")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const router = useRouter()

  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "products" || tab === "store") {
      router.push("/store")
    } else if (tab === "home") {
      router.push("/")
      router.refresh()
    } else {
      setActiveTab(tab)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-yellow-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <WalletCartSection />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 p-4 lg:p-8 space-y-8 pb-32 pt-40 lg:pt-44 max-w-7xl mx-auto">
        <PromoSlider />
        <NewsTicket />
        <BestSellingSection />
        <GameCardsGrid />

        {/* Additional Content for Desktop */}
        <section className="hidden lg:block">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-12">
            <FeaturesSection />
            <StatsSection />
          </div>
        </section>
      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
