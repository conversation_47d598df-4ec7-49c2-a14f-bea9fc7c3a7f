"use client"

import {
  CreditCard,
  HelpCircle,
  Home,
  Settings,
  User,
  Wallet,
} from "lucide-react"

interface DesktopFooterProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

export function DesktopFooter({ activeTab, onTabChange }: DesktopFooterProps) {
  const navItems = [
    { id: "profile", icon: <User className="h-6 w-6" />, label: "حسابي" },
    { id: "wallet", icon: <Wallet className="h-6 w-6" />, label: "المحفظة" },
    { id: "home", icon: <Home className="h-6 w-6" />, label: "الرئيسية" },
    { id: "requests", icon: <HelpCircle className="h-6 w-6" />, label: "طلباتي" },
    { id: "support", icon: <Settings className="h-6 w-6" />, label: "الدعم الفني" },
  ]

  return (
    <footer className="hidden lg:block relative z-10 bg-slate-800/50 backdrop-blur-xl border-t border-slate-700/50 mt-12">
      <div className="max-w-7xl mx-auto px-8 py-8">
        <div className="flex items-center justify-center gap-8">
          {navItems.map(({ id, icon, label }) => (
            <button
              key={id}
              onClick={() => onTabChange(id)}
              className={`flex flex-col items-center gap-2 p-4 rounded-2xl transition-all duration-300 hover:scale-105 ${
                activeTab === id
                  ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 shadow-lg"
                  : "text-slate-400 hover:text-white hover:bg-white/10"
              }`}
            >
              {icon}
              <span className="text-sm font-medium">{label}</span>
            </button>
          ))}
        </div>
      </div>
    </footer>
  )
}
