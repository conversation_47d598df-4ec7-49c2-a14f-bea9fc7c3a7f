import { Home, Store, Gift, User, Phone, Info } from "lucide-react"
import { MenuItem } from "@/lib/types"

export const menuItems: MenuItem[] = [
  { icon: <Home className="h-5 w-5" />, label: "الرئيسية", href: "#" },
  { icon: <Store className="h-5 w-5" />, label: "المتجر", href: "#" },
  { icon: <Gift className="h-5 w-5" />, label: "العروض", href: "#" },
  { icon: <User className="h-5 w-5" />, label: "حسابي", href: "#" },
  { icon: <Phone className="h-5 w-5" />, label: "اتصل بنا", href: "#" },
  { icon: <Info className="h-5 w-5" />, label: "من نحن", href: "#" },
]
